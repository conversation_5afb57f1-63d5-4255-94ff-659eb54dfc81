<#
.SYNOPSIS
    Creates an AD Computer Account from the AutoDeploy service.

.DESCRIPTION
    Creates an AD Computer Account from the AutoDeploy service, for Windows, Clusters and VDI virtual machines.

.PARAMETER jobId
    The ID of the job.

.PARAMETER objectName
    The name of the virtual machine or cluster.

.PARAMETER objectDescription
    The description of the virtual machine or cluster.

.PARAMETER vmOS
    The operating system of the virtual machine.

.PARAMETER domain
    The domain suffix for the AD object.

.PARAMETER ouPath
    The organizational unit path for the AD object.

.PARAMETER appType
    The type of application.

.PARAMETER clusterNodes
    List of cluster nodes for CLS/LST objects. Can be separated by comma (,), space ( ), or pipe (|). Supports both short names (NODE01) and FQDNs (NODE01.domain.com).

.EXAMPLE
    .\Create-ADObject.ps1 -jobId "123" -objectName "VM1" -objectDescription "Test VM" -vmOS "Windows" -domain "example.com" -ouPath "OU=Test,DC=example,DC=com" -appType "VDI"

.EXAMPLE
    .\Create-ADObject.ps1 -jobId "456" -objectName "CLS001" -objectDescription "Cluster VM" -vmOS "Windows" -domain "example.com" -ouPath "OU=Clusters,DC=example,DC=com" -appType "Cluster" -clusterNodes "NODE01,NODE02"
    .\Create-ADObject.ps1 -jobId "789" -objectName "LST001" -objectDescription "List VM" -vmOS "Windows" -domain "example.com" -ouPath "OU=Clusters,DC=example,DC=com" -appType "Cluster" -clusterNodes "NODE01 NODE02 NODE03"
    .\Create-ADObject.ps1 -jobId "101" -objectName "CLS002" -objectDescription "Cluster VM" -vmOS "Windows" -domain "example.com" -ouPath "OU=Clusters,DC=example,DC=com" -appType "Cluster" -clusterNodes "NODE01|NODE02|NODE03"
    .\Create-ADObject.ps1 -jobId "102" -objectName "CLS003" -objectDescription "Cluster VM" -vmOS "Windows" -domain "example.com" -ouPath "OU=Clusters,DC=example,DC=com" -appType "Cluster" -clusterNodes "NODE01.example.com,NODE02.example.com"

.NOTES
    File Name   : Create-ADObject.ps1
    Author      : Rudi van Zyl
    
    Version 1.5 - Added support for 'CLS' and 'LST' objects.
    Version 1.4 - Simplified further, removed unnecessary parameters and modules.
    Version 1.3 - Simplified and modernized the script.
    Version 1.2 - Refactored the script to be called instead of running on a schedule checking whether a job exists or not.
    Version 1.1 - Re-wrote Using AutoDeploy module
    Version 1.0.2 - Added issue_ref and issue_type to the API update.
    Version 1.0.1 - Added skipping AD object creation if this is a linux server.
    Version 1.0 - Base Script
#>
#Requires -Version 5
#Requires -Modules CredentialManager, ActiveDirectory

[CmdletBinding()]
param(
    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [string]$jobId,

    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [string]$objectName,

    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [string]$objectDescription,

    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [ValidateSet("Windows", "Linux")]
    [string]$vmOS,

    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [string]$domain,

    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [string]$ouPath,

    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [string]$appType,

    [Parameter(Mandatory = $false)]
    [string]$clusterNodes,

    # Accept any additional arguments to prevent parameter binding errors
    [Parameter(ValueFromRemainingArguments = $true)]
    [string[]]$RemainingArguments
)

function New-JsonReturn {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$success,

        [Parameter(Mandatory = $true)]
        [string]$status,

        [Parameter(Mandatory = $true)]
        [string]$message
    )

    $objectReturn = @{
        objectName   = $objectName
        Domain       = $domain
        Comment      = $Script:updateComment
        ouCreatedIn  = $ouPath
        timeStamp    = (Get-Date -Format "yyyy-MM-dd HH:mm:ss")
    }

    $jsonResponse = @{
        success = $success
        status  = $status
        message = $message
        data    = $objectReturn
    }

    return ($jsonResponse | ConvertTo-Json -Depth 3)
}

function Test-ADCredentials {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [System.Management.Automation.PSCredential]$Credential,

        [Parameter(Mandatory = $true)]
        [string]$Domain
    )

    try {
        Write-Host "Testing AD credentials for domain: $Domain" -ForegroundColor Cyan
        # Try a simple AD query to test credentials
        $null = Get-ADDomain -Server $Domain -Credential $Credential -ErrorAction Stop
        Write-Host "Credentials validated successfully" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Warning "Credential validation failed: $($_.Exception.Message)"
        return $false
    }
}

function Grant-ClusterNodePermissions {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [Microsoft.ActiveDirectory.Management.ADComputer]$adObject,

        [Parameter(Mandatory = $true)]
        [string]$clusterNodeList,

        [Parameter(Mandatory = $true)]
        [string]$domainName,

        [Parameter(Mandatory = $true)]
        [System.Management.Automation.PSCredential]$adCreds
    )

    Write-Host "Object name starts with CLS/LST, granting full control to cluster nodes..." -ForegroundColor Yellow

    # Split by multiple delimiters: comma, space, or pipe (period removed to support FQDNs)
    $nodeList = $clusterNodeList -split '[,\s\|]+' | ForEach-Object { $_.Trim() } | Where-Object { -not [string]::IsNullOrWhiteSpace($_) }

    foreach ($node in $nodeList) {
        if (-not [string]::IsNullOrWhiteSpace($node)) {
            try {
                $nodeAccount = Get-ADComputer -Identity $node -Server $domainName -Credential $adCreds -ErrorAction Stop
                $acl = Get-Acl -Path "AD:\$($adObject.DistinguishedName)"

                $accessRule = New-Object System.DirectoryServices.ActiveDirectoryAccessRule(
                    $nodeAccount.SID,
                    [System.DirectoryServices.ActiveDirectoryRights]::GenericAll,
                    [System.Security.AccessControl.AccessControlType]::Allow
                )

                $acl.SetAccessRule($accessRule)

                Set-Acl -Path "AD:\$($adObject.DistinguishedName)" -AclObject $acl

                Write-Host "Granted full control to $node on $($adObject.Name)" -ForegroundColor Green
            }
            catch {
                Write-Warning "Failed to grant permissions to $node`: $($_.Exception.Message)"
            }
        }
    }
}

try {
    $Script:updateComment = ""
    $jobStatus = "IN_PROGRESS"
    $success = $true
    $accountExists = $false

    # Log any unexpected remaining arguments for debugging
    if ($RemainingArguments -and $RemainingArguments.Count -gt 0) {
        Write-Host "Warning: Received unexpected arguments: $($RemainingArguments -join ', ')" -ForegroundColor Yellow
    }

    if ($vmOS -eq "Linux") {
        Write-Host "This is a Linux server - skipping AD account creation" -ForegroundColor DarkGreen
        $Script:updateComment = "Linux based server, no AD Object created."
        $jobStatus = "COMPLETED"
        return (New-JsonReturn -success $success -status $jobStatus -message $Script:updateComment)
    }

    Write-Host "Getting credentials for domain: $domain" -ForegroundColor Cyan
    $adCreds = if ($appType -eq "VDI") {
        Get-StoredCredential -Target "VDI"
    }
    else {
        Get-StoredCredential -Target $domain
    }

    if (-not $adCreds) {
        throw "Unable to retrieve stored credentials for target: $(if ($appType -eq 'VDI') { 'VDI' } else { $domain })"
    }

    # Test the credentials before proceeding
    if (-not (Test-ADCredentials -Credential $adCreds -Domain $domain)) {
        $errorMessage = "Credential validation failed for domain: $domain. Please check stored credentials."
        Write-Error $errorMessage
        return (New-JsonReturn -success "false" -status "ERROR" -message $errorMessage)
    }

    Write-Host "Checking if computer object '$objectName' already exists in domain '$domain'..." -ForegroundColor Cyan
    try {
        $existingAdObject = Get-ADComputer -Identity $objectName -Server $domain -Credential $adCreds -ErrorAction Stop
        if ($existingAdObject) {
            $warningMessage = "$objectName already exists in the $domain domain; skipping the creation step."
            Write-Warning $warningMessage
            return (New-JsonReturn -success "false" -status "WARNING" -message $warningMessage)
        }
    }
    catch [Microsoft.ActiveDirectory.Management.ADIdentityNotFoundException] {
        Write-Host "Computer name '$objectName' not found in the '$domain' domain; proceeding with object creation." -ForegroundColor DarkGreen
        $accountExists = $false
    }
    catch [Microsoft.ActiveDirectory.Management.ADServerDownException] {
        $errorMessage = "Unable to reach a domain controller for $domain"
        Write-Error $errorMessage
        return (New-JsonReturn -success "false" -status "ERROR" -message $errorMessage)
    }
    catch {
        $errorMessage = "Error checking for existing AD object: $($_.Exception.Message)"
        Write-Error $errorMessage
        return (New-JsonReturn -success "false" -status "ERROR" -message $errorMessage)
    }

    if (-not $accountExists) {
        Write-Host "Creating AD computer object '$objectName' in domain '$domain'..." -ForegroundColor Cyan
        try {
            $newAdComputerParams = @{
                Name            = $objectName
                SAMAccountName  = $objectName
                Path            = $ouPath
                Description     = $objectDescription
                Enabled         = $true
                Credential      = $adCreds
                Server          = $domain
                DNSHostName     = "$objectName.$domain"
                OtherAttributes = @{ 'comment' = $jobId }
            }

            New-ADComputer @newAdComputerParams
            Write-Host "Successfully created AD computer object '$objectName'" -ForegroundColor Green
        }
        catch {
            $errorMessage = "Failed to create AD computer object: $($_.Exception.Message)"
            Write-Error $errorMessage
            return (New-JsonReturn -success "false" -status "ERROR" -message $errorMessage)
        }

        Write-Host "Waiting for AD replication..." -ForegroundColor Cyan
        Start-Sleep -Seconds 10
    }

    try {
        $adObject = Get-ADComputer -Identity $objectName -Server $domain -Credential $adCreds -ErrorAction Stop

        if (($objectName.StartsWith("CLS", [System.StringComparison]::OrdinalIgnoreCase) -or
             $objectName.StartsWith("LST", [System.StringComparison]::OrdinalIgnoreCase)) -and
             -not [string]::IsNullOrWhiteSpace($clusterNodes)) {

            Grant-ClusterNodePermissions -adObject $adObject -clusterNodeList $clusterNodes -domainName $domain -adCreds $adCreds
        }

        $Script:updateComment = "AD Object $objectName created successfully in $domain"
        $jobStatus = "COMPLETED"
        Write-Host "AD object '$objectName' created successfully in the '$domain' domain" -ForegroundColor DarkGreen
    }
    catch {
        $errorMessage = "Failed to verify AD object creation: $($_.Exception.Message)"
        Write-Error $errorMessage
        return (New-JsonReturn -success "false" -status "ERROR" -message $errorMessage)
    }
}
catch {
    $errorMessage = "Unexpected error: $($_.Exception.Message)"
    Write-Error $errorMessage
    return (New-JsonReturn -success "false" -status "ERROR" -message $errorMessage)
}
finally {
    if (-not [string]::IsNullOrEmpty($Script:updateComment)) {
        New-JsonReturn -success $success -status $jobStatus -message $Script:updateComment
    }
}
