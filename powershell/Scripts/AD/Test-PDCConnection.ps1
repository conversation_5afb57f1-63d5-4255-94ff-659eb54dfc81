<#
.SYNOPSIS
    Test script to verify PDC connectivity and OU path validation for Create-ADObject.ps1

.DESCRIPTION
    This script tests the core functionality used by Create-ADObject.ps1:
    - Credential retrieval
    - PDC discovery
    - OU path validation
    - Basic AD connectivity

.PARAMETER domain
    The domain to test against

.PARAMETER ouPath
    The OU path to validate

.PARAMETER appType
    The application type (affects credential target)

.EXAMPLE
    .\Test-PDCConnection.ps1 -domain "mud.internal.co.za" -ouPath "OU=Windows Server,OU=Servers,OU=GTI,OU=Businesses" -appType "Shared"

.NOTES
    File Name   : Test-PDCConnection.ps1
    Author      : <PERSON><PERSON>
    Version     : 1.0 - Initial test script for PDC functionality
#>

[CmdletBinding()]
param(
    [Parameter(Mandatory = $true)]
    [string]$domain,

    [Parameter(Mandatory = $true)]
    [string]$ouPath,

    [Parameter(Mandatory = $false)]
    [string]$appType = "Shared"
)

function Get-ADPrimaryDomainController {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$Domain,

        [Parameter(Mandatory = $true)]
        [System.Management.Automation.PSCredential]$Credential
    )

    try {
        Write-Host "Retrieving PDC for domain: $Domain" -ForegroundColor Cyan
        $domainInfo = Get-ADDomain -Server $Domain -Credential $Credential -ErrorAction Stop
        $pdcEmulator = $domainInfo.PDCEmulator
        Write-Host "PDC found: $pdcEmulator" -ForegroundColor Green
        return $pdcEmulator
    }
    catch {
        Write-Warning "Failed to retrieve PDC for domain $Domain`: $($_.Exception.Message)"
        Write-Host "Falling back to domain name: $Domain" -ForegroundColor Yellow
        return $Domain
    }
}

function Test-ADOUPath {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$OUPath,

        [Parameter(Mandatory = $true)]
        [string]$Server,

        [Parameter(Mandatory = $true)]
        [string]$Domain,

        [Parameter(Mandatory = $true)]
        [System.Management.Automation.PSCredential]$Credential
    )

    try {
        Write-Host "Validating OU path: $OUPath" -ForegroundColor Cyan
        
        # Convert to full DN if needed
        $fullOuPath = if ($OUPath -notlike "*DC=*") {
            $domainDN = ($Domain -split '\.') | ForEach-Object { "DC=$_" }
            "$OUPath,$($domainDN -join ',')"
        } else {
            $OUPath
        }
        
        $null = Get-ADOrganizationalUnit -Identity $fullOuPath -Server $Server -Credential $Credential -ErrorAction Stop
        Write-Host "OU path validated successfully: $fullOuPath" -ForegroundColor Green
        return $fullOuPath
    }
    catch {
        Write-Warning "OU path validation failed: $($_.Exception.Message)"
        return $null
    }
}

try {
    Write-Host "=== Testing PDC Connection and OU Path Validation ===" -ForegroundColor Yellow
    Write-Host "Domain: $domain" -ForegroundColor White
    Write-Host "OU Path: $ouPath" -ForegroundColor White
    Write-Host "App Type: $appType" -ForegroundColor White
    Write-Host ""

    # Step 1: Get credentials
    Write-Host "Step 1: Retrieving stored credentials..." -ForegroundColor Cyan
    $credentialTarget = if ($appType -eq "VDI") { "VDI" } else { $domain }
    
    try {
        $adCreds = Get-StoredCredential -Target $credentialTarget -ErrorAction Stop
        if (-not $adCreds) {
            throw "No credentials found for target: $credentialTarget"
        }
        Write-Host "✓ Successfully retrieved credentials for target: $credentialTarget" -ForegroundColor Green
    }
    catch {
        Write-Error "✗ Failed to retrieve stored credentials for target '$credentialTarget': $($_.Exception.Message)"
        exit 1
    }

    # Step 2: Test basic domain connectivity
    Write-Host "`nStep 2: Testing domain connectivity..." -ForegroundColor Cyan
    try {
        $null = Get-ADDomain -Server $domain -Credential $adCreds -ErrorAction Stop
        Write-Host "✓ Successfully connected to domain: $domain" -ForegroundColor Green
    }
    catch {
        Write-Error "✗ Failed to connect to domain: $($_.Exception.Message)"
        exit 1
    }

    # Step 3: Get PDC
    Write-Host "`nStep 3: Discovering PDC..." -ForegroundColor Cyan
    $pdcServer = Get-ADPrimaryDomainController -Domain $domain -Credential $adCreds
    Write-Host "✓ Using PDC server: $pdcServer" -ForegroundColor Green

    # Step 4: Test PDC connectivity
    Write-Host "`nStep 4: Testing PDC connectivity..." -ForegroundColor Cyan
    try {
        $null = Get-ADDomain -Server $pdcServer -Credential $adCreds -ErrorAction Stop
        Write-Host "✓ Successfully connected to PDC: $pdcServer" -ForegroundColor Green
    }
    catch {
        Write-Error "✗ Failed to connect to PDC: $($_.Exception.Message)"
        exit 1
    }

    # Step 5: Validate OU path
    Write-Host "`nStep 5: Validating OU path..." -ForegroundColor Cyan
    $validatedOuPath = Test-ADOUPath -OUPath $ouPath -Server $pdcServer -Domain $domain -Credential $adCreds
    if ($validatedOuPath) {
        Write-Host "✓ OU path validation successful" -ForegroundColor Green
        Write-Host "  Full DN: $validatedOuPath" -ForegroundColor Gray
    } else {
        Write-Error "✗ OU path validation failed"
        exit 1
    }

    # Step 6: Test computer object query (should fail for non-existent object)
    Write-Host "`nStep 6: Testing computer object query capability..." -ForegroundColor Cyan
    try {
        $testComputerName = "NONEXISTENT-TEST-$(Get-Random -Minimum 1000 -Maximum 9999)"
        $null = Get-ADComputer -Identity $testComputerName -Server $pdcServer -Credential $adCreds -ErrorAction Stop
        Write-Warning "Unexpected: Test computer object exists: $testComputerName"
    }
    catch [Microsoft.ActiveDirectory.Management.ADIdentityNotFoundException] {
        Write-Host "✓ Computer object query working correctly (test object not found as expected)" -ForegroundColor Green
    }
    catch {
        Write-Error "✗ Computer object query failed: $($_.Exception.Message)"
        exit 1
    }

    Write-Host "`n=== All Tests Passed Successfully ===" -ForegroundColor Green
    Write-Host "The Create-ADObject.ps1 script should work with these parameters." -ForegroundColor Green
}
catch {
    Write-Error "Unexpected error during testing: $($_.Exception.Message)"
    exit 1
}
